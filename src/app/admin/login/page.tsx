'use client';
/* global window setTimeout */

import React, { useState } from 'react';
// Note: This component runs on the client ('use client'), guard window usage for lint.
import {
  BsBook,
  BsBuilding,
  BsEye,
  BsEyeSlash,
  BsPeople,
  BsShield,
} from 'react-icons/bs';

import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Form,
  FormActions,
  FormField,
  FormHelp,
  FormLabel,
  Input,
} from '@/components/common';

export default function AdminLogin() {
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));

    // Redirect to dashboard after successful login
    if (typeof window !== 'undefined') {
      window.location.href = '/admin/dashboard';
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <div className='relative flex items-center justify-center min-h-screen px-4 py-10 bg-brand-cream'>
      {/* Decorative blobs */}
      <div className='absolute inset-0 overflow-hidden pointer-events-none'>
        <div className='absolute rounded-full -top-40 -right-40 h-80 w-80 animate-pulse bg-gradient-to-br from-brand-mint/30 to-brand-yellow/30 blur-3xl' />
        <div className='absolute delay-1000 rounded-full -bottom-40 -left-40 h-80 w-80 animate-pulse bg-gradient-to-br from-brand-sage/30 to-brand-coral/30 blur-3xl' />
      </div>

      <div className='relative w-full max-w-md'>
        <div className='mb-8 text-center'>
          <div className='inline-flex items-center justify-center w-16 h-16 mb-4 shadow-lg rounded-2xl bg-gradient-to-r from-brand-mint to-brand-yellow ring-1 ring-black/5'>
            <BsBuilding className='w-8 h-8 text-white' />
          </div>
          <h1 className='mb-1 text-3xl font-bold tracking-tight text-gray-900'>
            Cresce Feliz
          </h1>
          <p className='text-sm font-medium text-gray-600'>
            Painel Administrativo
          </p>
        </div>

        <Card
          shadow='lg'
          padding='lg'
          className='border bg-white/70 backdrop-blur-xl border-brand-sage/40'
        >
          <CardHeader>
            <CardTitle className='text-2xl'>Bem-vindo</CardTitle>
            <CardDescription>
              Aceda à sua conta de administrador
            </CardDescription>
          </CardHeader>
          <CardContent className='space-y-6'>
            <Form onSubmit={handleSubmit}>
              <FormField>
                <FormLabel htmlFor='email'>Email</FormLabel>
                <Input
                  id='email'
                  type='email'
                  name='email'
                  placeholder='<EMAIL>'
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                  className='bg-white/60 backdrop-blur-sm placeholder:text-brand-gray/70'
                />
              </FormField>

              <FormField>
                <FormLabel htmlFor='password'>Palavra-passe</FormLabel>
                <div className='relative'>
                  <Input
                    id='password'
                    type={showPassword ? 'text' : 'password'}
                    name='password'
                    placeholder='••••••••'
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                    className='pr-11 bg-white/60 backdrop-blur-sm placeholder:text-brand-gray/70'
                  />
                  <button
                    type='button'
                    aria-label={
                      showPassword
                        ? 'Esconder palavra-passe'
                        : 'Mostrar palavra-passe'
                    }
                    onClick={() => setShowPassword(!showPassword)}
                    className='absolute inset-y-0 flex items-center transition-colors right-2 text-brand-gray hover:text-brand-mint'
                  >
                    {showPassword ? (
                      <BsEyeSlash className='w-5 h-5' />
                    ) : (
                      <BsEye className='w-5 h-5' />
                    )}
                  </button>
                </div>
                <FormHelp>
                  Use credenciais fornecidas pelo administrador.
                </FormHelp>
              </FormField>

              <div className='flex items-center justify-between pt-1'>
                <label className='flex items-center cursor-pointer'>
                  <input
                    type='checkbox'
                    className='w-4 h-4 text-green-600 border-gray-300 rounded focus:ring-green-500 focus:ring-offset-0'
                  />
                  <span className='ml-2 text-xs font-medium text-brand-gray'>
                    Lembrar-me
                  </span>
                </label>
                <button
                  type='button'
                  className='text-xs font-medium transition-colors text-brand-mint underline-offset-2 hover:text-brand-mint/80 hover:underline'
                  aria-label='Recuperar palavra-passe (brevemente)'
                  disabled
                >
                  Esqueceu a palavra-passe?
                </button>
              </div>

              <FormActions className='mt-2'>
                <Button
                  type='submit'
                  className='w-full'
                  size='lg'
                  isLoading={isLoading}
                  loadingText='A processar...'
                >
                  Entrar
                </Button>
              </FormActions>
            </Form>
          </CardContent>
          <CardFooter className='flex flex-col'>
            <div className='w-full pt-5 mt-2 border-t border-gray-200'>
              <p className='mb-4 text-xs text-center text-brand-gray'>
                Plataforma para gestão de infantários
              </p>
              <div className='grid grid-cols-3 gap-4'>
                <div className='text-center'>
                  <div className='flex items-center justify-center w-10 h-10 mx-auto mb-2 rounded-lg bg-brand-sage/40'>
                    <BsShield className='w-5 h-5 text-brand-mint' />
                  </div>
                  <p className='text-[11px] font-medium text-gray-600'>
                    Seguro
                  </p>
                </div>
                <div className='text-center'>
                  <div className='flex items-center justify-center w-10 h-10 mx-auto mb-2 rounded-lg bg-brand-yellow/30'>
                    <BsPeople className='w-5 h-5 text-brand-yellow' />
                  </div>
                  <p className='text-[11px] font-medium text-gray-600'>
                    Gestão
                  </p>
                </div>
                <div className='text-center'>
                  <div className='flex items-center justify-center w-10 h-10 mx-auto mb-2 rounded-lg bg-brand-coral/30'>
                    <BsBook className='w-5 h-5 text-brand-coral' />
                  </div>
                  <p className='text-[11px] font-medium text-gray-600'>
                    Educação
                  </p>
                </div>
              </div>
            </div>
          </CardFooter>
        </Card>

        <div className='mt-8 text-center'>
          <p className='text-xs text-brand-gray'>
            © 2025 Cresce Feliz. Todos os direitos reservados.
          </p>
        </div>
      </div>
    </div>
  );
}
